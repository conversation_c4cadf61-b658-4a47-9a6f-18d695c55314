import { pgTable, text, timestamp, integer, boolean, uuid, jsonb, serial } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Users table for Clerk authentication
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  clerkId: text('clerk_id').notNull().unique(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Life milestones for priority scoring
export const milestones = pgTable('milestones', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  title: text('title').notNull(),
  description: text('description'),
  targetDate: text('target_date'), // ISO date string
  weight: integer('weight').notNull().default(3), // 1-5 scale
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Tasks with hierarchy support
export const tasks = pgTable('tasks', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  parentTaskId: uuid('parent_task_id').references(() => tasks.id),
  title: text('title').notNull(),
  description: text('description'),
  dueDate: text('due_date'), // ISO date string
  priorityScore: integer('priority_score').notNull().default(0),
  isHighlight: boolean('is_highlight').notNull().default(false),
  status: text('status').notNull().default('todo'), // 'todo' | 'doing' | 'done'
  toolAction: jsonb('tool_action'), // Optional JSON payload for MCP tool actions
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Tool connections for external integrations
export const toolConnections = pgTable('tool_connections', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  provider: text('provider').notNull(), // 'github' | 'notion' | 'gcal' | etc.
  accessToken: text('access_token').notNull(),
  scopes: text('scopes').array(), // Array of permission scopes
  metadata: jsonb('metadata'), // Provider-specific data
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  milestones: many(milestones),
  tasks: many(tasks),
  toolConnections: many(toolConnections),
}));

export const milestonesRelations = relations(milestones, ({ one }) => ({
  user: one(users, {
    fields: [milestones.userId],
    references: [users.id],
  }),
}));

export const tasksRelations = relations(tasks, ({ one, many }) => ({
  user: one(users, {
    fields: [tasks.userId],
    references: [users.id],
  }),
  parent: one(tasks, {
    fields: [tasks.parentTaskId],
    references: [tasks.id],
  }),
  subtasks: many(tasks),
}));

export const toolConnectionsRelations = relations(toolConnections, ({ one }) => ({
  user: one(users, {
    fields: [toolConnections.userId],
    references: [users.id],
  }),
}));