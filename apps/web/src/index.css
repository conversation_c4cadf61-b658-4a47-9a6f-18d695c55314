@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "<PERSON>", "<PERSON><PERSON><PERSON>", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-background text-foreground;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  
  /* Orion Color Palette */
  --orion-background: #f8f5f2;
  --orion-headline: #232323;
  --orion-paragraph: #222525;
  --orion-button: #078080;
  --orion-button-text: #232323;
  --orion-stroke: #232323;
  --orion-main: #fffffe;
  --orion-highlight: #078080;
  --orion-secondary: #f45d48;
  --orion-tertiary: #f8f5f2;
  
  /* Updated theme colors using Orion palette */
  --background: var(--orion-background);
  --foreground: var(--orion-headline);
  --card: var(--orion-main);
  --card-foreground: var(--orion-paragraph);
  --popover: var(--orion-main);
  --popover-foreground: var(--orion-paragraph);
  --primary: var(--orion-highlight);
  --primary-foreground: var(--orion-main);
  --secondary: var(--orion-secondary);
  --secondary-foreground: var(--orion-main);
  --muted: var(--orion-tertiary);
  --muted-foreground: var(--orion-paragraph);
  --accent: var(--orion-highlight);
  --accent-foreground: var(--orion-main);
  --destructive: oklch(0.577 0.245 27.325);
  --border: var(--orion-stroke);
  --input: var(--orion-main);
  --ring: var(--orion-highlight);
  --chart-1: var(--orion-highlight);
  --chart-2: var(--orion-secondary);
  --chart-3: var(--orion-paragraph);
  --chart-4: var(--orion-button);
  --chart-5: var(--orion-headline);
  --sidebar: var(--orion-main);
  --sidebar-foreground: var(--orion-headline);
  --sidebar-primary: var(--orion-highlight);
  --sidebar-primary-foreground: var(--orion-main);
  --sidebar-accent: var(--orion-tertiary);
  --sidebar-accent-foreground: var(--orion-headline);
  --sidebar-border: var(--orion-stroke);
  --sidebar-ring: var(--orion-highlight);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .scrollbar-hide {
    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}
