"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader, X, <PERSON>rk<PERSON>, Clock, CheckCircle, ArrowRight, Lightbulb } from "lucide-react";

interface Subtask {
  title: string;
  description: string;
  estimatedTime: string;
  priority: number;
  suggestedTool?: string;
  dependencies?: string[];
}

interface AutoPlan {
  subtasks: Subtask[];
  estimatedTotalTime: string;
  successCriteria: string[];
  tips?: string[];
}

interface AutoPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskTitle: string;
  taskDescription?: string;
  onSubtasksCreate: (subtasks: Subtask[]) => void;
}

export default function AutoPlanModal({
  isOpen,
  onClose,
  taskTitle,
  taskDescription,
  onSubtasksCreate
}: AutoPlanModalProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [autoPlan, setAutoPlan] = useState<AutoPlan | null>(null);
  const [selectedSubtasks, setSelectedSubtasks] = useState<Set<number>>(new Set());

  const generateAutoPlan = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/auto-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskTitle,
          taskDescription,
          context: 'Solo founder working on startup tasks',
        }),
      });

      if (!response.ok) throw new Error('Failed to generate Auto Plan');

      const plan: AutoPlan = await response.json();
      setAutoPlan(plan);
      // Select all subtasks by default
      setSelectedSubtasks(new Set(Array.from({ length: plan.subtasks.length }, (_, i) => i)));
    } catch (error) {
      console.error('Error generating Auto Plan:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const toggleSubtask = (index: number) => {
    const newSelected = new Set(selectedSubtasks);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedSubtasks(newSelected);
  };

  const handleCreateSubtasks = () => {
    if (!autoPlan) return;
    
    const selectedSubtaskList = autoPlan.subtasks.filter((_, index) => 
      selectedSubtasks.has(index)
    );
    
    onSubtasksCreate(selectedSubtaskList);
    handleClose();
  };

  const handleClose = () => {
    setAutoPlan(null);
    setSelectedSubtasks(new Set());
    setIsGenerating(false);
    onClose();
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 5: return 'bg-red-500';
      case 4: return 'bg-orange-500';
      case 3: return 'bg-yellow-500';
      case 2: return 'bg-blue-500';
      case 1: return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 5: return 'Critical';
      case 4: return 'High';
      case 3: return 'Medium';
      case 2: return 'Low';
      case 1: return 'Optional';
      default: return 'Medium';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Auto Plan: {taskTitle}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {!autoPlan && !isGenerating && (
            <div className="text-center py-8">
              <Sparkles className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Break Down Your Task</h3>
              <p className="text-muted-foreground mb-6">
                Let AI analyze your task and create a step-by-step plan with actionable subtasks.
              </p>
              <Button onClick={generateAutoPlan} className="gap-2">
                <Sparkles className="h-4 w-4" />
                Generate Auto Plan
              </Button>
            </div>
          )}

          {isGenerating && (
            <div className="text-center py-8">
              <Loader className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Generating Your Plan...</h3>
              <p className="text-muted-foreground">
                AI is analyzing your task and creating actionable subtasks.
              </p>
            </div>
          )}

          {autoPlan && (
            <div className="space-y-6">
              {/* Overview */}
              <div className="flex gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Total Time: {autoPlan.estimatedTotalTime}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  <span>{autoPlan.subtasks.length} Subtasks</span>
                </div>
              </div>

              {/* Subtasks */}
              <div className="space-y-3">
                <h3 className="font-medium flex items-center gap-2">
                  <ArrowRight className="h-4 w-4" />
                  Subtasks
                </h3>
                
                {autoPlan.subtasks.map((subtask, index) => (
                  <Card 
                    key={index} 
                    className={`cursor-pointer transition-all ${
                      selectedSubtasks.has(index) 
                        ? 'ring-2 ring-primary bg-primary/5' 
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => toggleSubtask(index)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 ${
                          selectedSubtasks.has(index) 
                            ? 'bg-primary border-primary' 
                            : 'border-muted-foreground'
                        }`}>
                          {selectedSubtasks.has(index) && (
                            <CheckCircle className="h-3 w-3 text-primary-foreground" />
                          )}
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <h4 className="font-medium">{subtask.title}</h4>
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant="secondary" 
                                className={`${getPriorityColor(subtask.priority)} text-white text-xs`}
                              >
                                {getPriorityLabel(subtask.priority)}
                              </Badge>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground">
                            {subtask.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {subtask.estimatedTime}
                            </div>
                            {subtask.suggestedTool && (
                              <div className="flex items-center gap-1">
                                <span>Tool: {subtask.suggestedTool}</span>
                              </div>
                            )}
                          </div>
                          
                          {subtask.dependencies && subtask.dependencies.length > 0 && (
                            <div className="text-xs text-muted-foreground">
                              <span>Depends on: {subtask.dependencies.join(', ')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Success Criteria */}
              <div className="space-y-2">
                <h3 className="font-medium flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Success Criteria
                </h3>
                <ul className="space-y-1">
                  {autoPlan.successCriteria.map((criteria, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                      <div className="w-1 h-1 bg-primary rounded-full" />
                      {criteria}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Tips */}
              {autoPlan.tips && autoPlan.tips.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    Tips
                  </h3>
                  <ul className="space-y-1">
                    {autoPlan.tips.map((tip, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                        <div className="w-1 h-1 bg-yellow-500 rounded-full" />
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between pt-4 border-t">
                <Button variant="outline" onClick={() => setAutoPlan(null)}>
                  Regenerate Plan
                </Button>
                <div className="space-x-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateSubtasks}
                    disabled={selectedSubtasks.size === 0}
                  >
                    Create {selectedSubtasks.size} Subtask{selectedSubtasks.size !== 1 ? 's' : ''}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}