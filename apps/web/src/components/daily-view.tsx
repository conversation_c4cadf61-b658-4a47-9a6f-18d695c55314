"use client";

import { useState, useEffect, useRef } from "react";
import { Diamond, Star, Clock, MoreHorizontal, Sparkles, GripVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import Chatbot from "@/components/chatbot";

interface Task {
  id: string;
  title: string;
  description?: string;
  time: string; // HH:MM format
  isCompleted: boolean;
  isHighlight: boolean;
  priorityScore: number;
  status: 'todo' | 'doing' | 'done';
}

interface DailyViewProps {
  tasks: Task[];
  onTaskToggle: (taskId: string) => void;
  onTaskHighlight: (taskId: string) => void;
  onTaskDetails: (taskId: string) => void;
  onAutoLan: (taskId: string) => void;
  onTaskMove: (taskId: string, newTime: string) => void;
}

const TIMELINE_HOURS = Array.from({ length: 24 }, (_, i) => 
  `${i.toString().padStart(2, '0')}:00`
);

const DISPLAY_HOURS = [
  '8:00 AM - 10:00 AM', '10:00 AM - 12:00 PM', '12:00 PM - 2:00 PM',
  '2:00 PM - 4:00 PM', '4:00 PM - 6:00 PM', '6:00 PM - 8:00 PM',
  '8:00 PM - 10:00 PM'
];

export default function DailyView({
  tasks,
  onTaskToggle,
  onTaskHighlight,
  onTaskDetails,
  onAutoLan,
  onTaskMove
}: DailyViewProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [hoveredTask, setHoveredTask] = useState<string | null>(null);
  const [sidebarWidth, setSidebarWidth] = useState(384); // Default 384px (w-96)
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Resize handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.right - e.clientX;
      const minWidth = 280; // Minimum sidebar width
      const maxWidth = containerRect.width * 0.6; // Maximum 60% of container

      setSidebarWidth(Math.max(minWidth, Math.min(maxWidth, newWidth)));
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  const formatTime12Hour = (time24: string) => {
    const [hours, minutes] = time24.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  const getTasksForTimeRange = (startHour: number, endHour: number) => {
    return tasks.filter(task => {
      const taskHour = parseInt(task.time.split(':')[0]);
      return taskHour >= startHour && taskHour < endHour;
    });
  };

  const TaskCard = ({ task }: { task: Task }) => {
    const handleSwipe = (direction: 'left' | 'right') => {
      if (direction === 'right') {
        // Move to tomorrow logic would go here
        console.log('Moving task to tomorrow:', task.id);
      }
    };

    return (
      <Card 
        className={`mb-2 transition-all duration-200 hover:shadow-md cursor-pointer
          ${task.isHighlight ? 'ring-2 ring-primary' : ''}
          ${task.status === 'done' ? 'opacity-60' : ''}
          ${hoveredTask === task.id ? 'scale-105' : ''}
        `}
        onMouseEnter={() => setHoveredTask(task.id)}
        onMouseLeave={() => setHoveredTask(null)}
      >
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            <Checkbox 
              checked={task.isCompleted}
              onCheckedChange={() => onTaskToggle(task.id)}
              className="flex-shrink-0"
            />
            
            <button
              onClick={() => onTaskDetails(task.id)}
              className="flex-shrink-0 text-primary hover:text-primary/80"
            >
              <Diamond size={16} className={task.isCompleted ? 'fill-current' : ''} />
            </button>
            
            <div className="flex-1 min-w-0">
              <h3 className={`font-medium text-sm leading-tight
                ${task.isCompleted ? 'line-through text-muted-foreground' : 'text-foreground'}
              `}>
                {task.title}
              </h3>
              {task.description && (
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {task.description}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-1 flex-shrink-0">
              <span className="text-xs text-muted-foreground">
                {formatTime12Hour(task.time)}
              </span>
              
              <button
                onClick={() => onTaskHighlight(task.id)}
                className={`p-1 rounded transition-colors
                  ${task.isHighlight 
                    ? 'text-yellow-500 hover:text-yellow-600' 
                    : 'text-muted-foreground hover:text-foreground'
                  }
                `}
              >
                <Star size={14} className={task.isHighlight ? 'fill-current' : ''} />
              </button>
              
              <button
                onClick={() => onAutoLan(task.id)}
                className="p-1 text-muted-foreground hover:text-primary transition-colors"
                title="Auto Plan"
              >
                <Sparkles size={14} />
              </button>
            </div>
          </div>
          
          {hoveredTask === task.id && (
            <div className="mt-2 pt-2 border-t border-border">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Priority: {task.priorityScore}</span>
                <span className="capitalize">{task.status}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };



  return (
    <div ref={containerRef} className="h-full bg-background flex relative">
      {/* Main Content */}
      <div
        className="flex-1 h-full overflow-hidden"
        style={{ width: `calc(100% - ${sidebarWidth}px)` }}
      >
        <div className="h-full overflow-y-auto scrollbar-hide">
          <div className="max-w-2xl mx-auto p-6">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-foreground mb-2">DAILY VIEW</h1>
              <p className="text-muted-foreground">• Daily Tasks •</p>
            </div>

            <div className="space-y-6 pb-6">
              {DISPLAY_HOURS.map((displayHour, index) => {
                const startHour = (index * 2) + 8; // 8, 10, 12, 14, 16, 18, 20
                const endHour = startHour + 2;
                const timeRangeTasks = getTasksForTimeRange(startHour, endHour);
                const isCurrentTimeRange = currentTime.getHours() >= startHour && currentTime.getHours() < endHour;

                return (
                  <div key={displayHour} className={`border-l-4 pl-6 relative
                    ${isCurrentTimeRange ? 'border-primary' : 'border-border'}
                  `}>
                    <div className={`text-lg font-medium mb-4 flex items-center gap-2
                      ${isCurrentTimeRange ? 'text-primary' : 'text-muted-foreground'}
                    `}>
                      {isCurrentTimeRange && <Clock size={20} />}
                      {displayHour}
                    </div>

                    <div className="space-y-3">
                      {timeRangeTasks.length > 0 ? (
                        timeRangeTasks.map((task) => (
                          <TaskCard key={task.id} task={task} />
                        ))
                      ) : (
                        <div className="h-16 border-2 border-dashed border-border rounded-lg
                          flex items-center justify-center text-muted-foreground text-sm">
                          Drop task here
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Resize Handle */}
      <div
        className={`w-1 bg-border hover:bg-primary/50 cursor-col-resize flex items-center justify-center transition-colors relative group
          ${isResizing ? 'bg-primary' : ''}
        `}
        onMouseDown={handleMouseDown}
      >
        <div className="absolute inset-y-0 -inset-x-1 flex items-center justify-center">
          <GripVertical className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
        </div>
      </div>

      {/* Chatbot Sidebar */}
      <div
        className="border-l border-border p-4 flex flex-col h-full overflow-hidden"
        style={{ width: `${sidebarWidth}px` }}
      >
        <Chatbot className="flex-1 h-full" />
      </div>
    </div>
  );
}