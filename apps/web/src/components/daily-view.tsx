"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, Clock, MoreHorizontal, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";

interface Task {
  id: string;
  title: string;
  description?: string;
  time: string; // HH:MM format
  isCompleted: boolean;
  isHighlight: boolean;
  priorityScore: number;
  status: 'todo' | 'doing' | 'done';
}

interface DailyViewProps {
  tasks: Task[];
  onTaskToggle: (taskId: string) => void;
  onTaskHighlight: (taskId: string) => void;
  onTaskDetails: (taskId: string) => void;
  onAutoLan: (taskId: string) => void;
  onTaskMove: (taskId: string, newTime: string) => void;
}

const TIMELINE_HOURS = Array.from({ length: 24 }, (_, i) => 
  `${i.toString().padStart(2, '0')}:00`
);

const DISPLAY_HOURS = [
  '8:00 AM', '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
  '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM',
  '6:00 PM', '7:00 PM', '8:00 PM'
];

export default function DailyView({
  tasks,
  onTaskToggle,
  onTaskHighlight,
  onTaskDetails,
  onAutoLan,
  onTaskMove
}: DailyViewProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [hoveredTask, setHoveredTask] = useState<string | null>(null);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const formatTime12Hour = (time24: string) => {
    const [hours, minutes] = time24.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  const getTasksForHour = (hour: string) => {
    return tasks.filter(task => task.time.startsWith(hour.slice(0, 2)));
  };

  const TaskCard = ({ task }: { task: Task }) => {
    const handleSwipe = (direction: 'left' | 'right') => {
      if (direction === 'right') {
        // Move to tomorrow logic would go here
        console.log('Moving task to tomorrow:', task.id);
      }
    };

    return (
      <Card 
        className={`mb-2 transition-all duration-200 hover:shadow-md cursor-pointer
          ${task.isHighlight ? 'ring-2 ring-primary' : ''}
          ${task.status === 'done' ? 'opacity-60' : ''}
          ${hoveredTask === task.id ? 'scale-105' : ''}
        `}
        onMouseEnter={() => setHoveredTask(task.id)}
        onMouseLeave={() => setHoveredTask(null)}
      >
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            <Checkbox 
              checked={task.isCompleted}
              onCheckedChange={() => onTaskToggle(task.id)}
              className="flex-shrink-0"
            />
            
            <button
              onClick={() => onTaskDetails(task.id)}
              className="flex-shrink-0 text-primary hover:text-primary/80"
            >
              <Diamond size={16} className={task.isCompleted ? 'fill-current' : ''} />
            </button>
            
            <div className="flex-1 min-w-0">
              <h3 className={`font-medium text-sm leading-tight
                ${task.isCompleted ? 'line-through text-muted-foreground' : 'text-foreground'}
              `}>
                {task.title}
              </h3>
              {task.description && (
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {task.description}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-1 flex-shrink-0">
              <span className="text-xs text-muted-foreground">
                {formatTime12Hour(task.time)}
              </span>
              
              <button
                onClick={() => onTaskHighlight(task.id)}
                className={`p-1 rounded transition-colors
                  ${task.isHighlight 
                    ? 'text-yellow-500 hover:text-yellow-600' 
                    : 'text-muted-foreground hover:text-foreground'
                  }
                `}
              >
                <Star size={14} className={task.isHighlight ? 'fill-current' : ''} />
              </button>
              
              <button
                onClick={() => onAutoLan(task.id)}
                className="p-1 text-muted-foreground hover:text-primary transition-colors"
                title="Auto Plan"
              >
                <Sparkles size={14} />
              </button>
            </div>
          </div>
          
          {hoveredTask === task.id && (
            <div className="mt-2 pt-2 border-t border-border">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Priority: {task.priorityScore}</span>
                <span className="capitalize">{task.status}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const TaskSidebar = () => {
    const sortedTasks = [...tasks]
      .sort((a, b) => {
        if (a.isHighlight && !b.isHighlight) return -1;
        if (!a.isHighlight && b.isHighlight) return 1;
        return b.priorityScore - a.priorityScore;
      })
      .slice(0, 4);

    return (
      <div className="w-80 bg-card border-r border-border p-4 overflow-y-auto">
        <h2 className="font-semibold text-lg mb-4 text-foreground">Today&apos;s Tasks</h2>
        
        <div className="space-y-2">
          {sortedTasks.map((task) => (
            <div key={task.id} className="p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <Diamond size={12} className={`text-primary ${task.isCompleted ? 'fill-current' : ''}`} />
                <span className="text-sm font-medium">{task.title}</span>
                {task.isHighlight && <Star size={12} className="text-yellow-500 fill-current" />}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {formatTime12Hour(task.time)}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-3 bg-primary/10 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-3 h-3 bg-primary rounded-full"></div>
            <span className="text-sm font-medium">Quick Add</span>
          </div>
          <p className="text-xs text-muted-foreground">
            Tap + to add a task at 3pm...
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full bg-background">
      <TaskSidebar />
      
      <div className="flex-1 overflow-auto">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">DAILY VIEW</h1>
            <p className="text-muted-foreground">• Daily Tasks •</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {DISPLAY_HOURS.map((displayHour, index) => {
              const hour24 = (index + 8).toString().padStart(2, '0');
              const hourTasks = getTasksForHour(hour24);
              const isCurrentHour = currentTime.getHours() === (index + 8);
              
              return (
                <div key={displayHour} className={`border-l-2 pl-4 relative
                  ${isCurrentHour ? 'border-primary' : 'border-border'}
                `}>
                  <div className={`text-sm font-medium mb-3 flex items-center gap-2
                    ${isCurrentHour ? 'text-primary' : 'text-muted-foreground'}
                  `}>
                    {isCurrentHour && <Clock size={16} />}
                    {displayHour}
                  </div>
                  
                  <div className="space-y-2">
                    {hourTasks.length > 0 ? (
                      hourTasks.map((task) => (
                        <TaskCard key={task.id} task={task} />
                      ))
                    ) : (
                      <div className="h-12 border-2 border-dashed border-border rounded-lg 
                        flex items-center justify-center text-muted-foreground text-xs">
                        Drop task here
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}