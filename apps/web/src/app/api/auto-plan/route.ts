import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateObject } from 'ai';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

const SubtaskSchema = z.object({
  title: z.string().describe('Clear, actionable subtask title'),
  description: z.string().describe('Detailed description of what needs to be done'),
  estimatedTime: z.string().describe('Estimated time to complete (e.g., "30 minutes", "2 hours")'),
  priority: z.number().min(1).max(5).describe('Priority level from 1 (low) to 5 (high)'),
  suggestedTool: z.string().optional().describe('Suggested tool or service to help with this task'),
  dependencies: z.array(z.string()).optional().describe('List of subtask titles this depends on'),
});

const AutoPlanSchema = z.object({
  subtasks: z.array(SubtaskSchema),
  estimatedTotalTime: z.string().describe('Total estimated time for all subtasks'),
  successCriteria: z.array(z.string()).describe('How to know when the main task is complete'),
  tips: z.array(z.string()).optional().describe('Additional tips or considerations'),
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { taskTitle, taskDescription, context } = await req.json();

    if (!taskTitle) {
      return new Response('Task title is required', { status: 400 });
    }

    const result = await generateObject({
      model: openrouter.chat('google/gemini-2.0-flash-exp:free'),
      schema: AutoPlanSchema,
      prompt: `
        Create an Auto Plan for the following task by breaking it down into actionable subtasks.
        
        Task: "${taskTitle}"
        Description: "${taskDescription || 'No additional description provided'}"
        Context: "${context || 'No additional context provided'}"
        
        Guidelines:
        1. Break down the task into 3-8 specific, actionable subtasks
        2. Each subtask should be something that can be completed in one sitting
        3. Order subtasks logically (dependencies should come first)
        4. Provide realistic time estimates
        5. Suggest tools, services, or resources that could help
        6. Include any dependencies between subtasks
        7. Focus on practical steps a solo founder can take
        
        For technical tasks, consider:
        - Research and planning phases
        - Implementation steps
        - Testing and validation
        - Documentation
        
        For business tasks, consider:
        - Preparation and research
        - Execution steps
        - Follow-up actions
        - Tracking and measurement
        
        Make the plan actionable and specific to help the user make progress immediately.
      `,
    });

    return Response.json(result.object);
  } catch (error) {
    console.error('Auto Plan API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}