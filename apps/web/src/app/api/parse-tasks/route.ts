import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateObject } from 'ai';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

const TaskSchema = z.object({
  title: z.string().describe('Clear, actionable task title'),
  description: z.string().optional().describe('Additional context or details'),
  dueDate: z.string().optional().describe('Due date in ISO format if mentioned'),
  estimatedTime: z.string().optional().describe('Estimated time to complete (e.g., "2 hours", "30 minutes")'),
  priority: z.number().min(1).max(5).optional().describe('Priority level from 1 (low) to 5 (high)'),
});

const ParsedTasksSchema = z.object({
  tasks: z.array(TaskSchema),
  confidence: z.number().min(0).max(1).describe('Confidence level of the parsing'),
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { input } = await req.json();

    if (!input || typeof input !== 'string') {
      return new Response('Invalid input', { status: 400 });
    }

    const result = await generateObject({
      model: openrouter.chat('google/gemini-2.0-flash-exp:free'),
      schema: ParsedTasksSchema,
      prompt: `
        Parse the following natural language input into structured tasks for a solo founder's to-do list.
        
        Input: "${input}"
        
        Guidelines:
        - Extract clear, actionable tasks
        - Infer due dates from time expressions (e.g., "tomorrow", "next week", "Monday")
        - Estimate time requirements when possible
        - Assign priority based on urgency and importance
        - Break down complex requests into multiple tasks if appropriate
        - For sequential tasks (e.g., "do X then Y"), create separate tasks
        
        Examples:
        - "Finish Stripe onboarding, then email investors tomorrow" → 
          Task 1: "Complete Stripe onboarding setup", priority: 4
          Task 2: "Email update to investors", dueDate: tomorrow, priority: 4
        
        - "Schedule dentist appointment for next week" →
          Task 1: "Schedule dentist appointment", dueDate: next week, priority: 2
        
        Return tasks that are specific, actionable, and helpful for a busy founder.
      `,
    });

    return Response.json(result.object);
  } catch (error) {
    console.error('Parse tasks API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}