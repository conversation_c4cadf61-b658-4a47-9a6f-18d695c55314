"use client"

import { useState } from "react";
// import { useUser } from "@clerk/nextjs";
import DailyView from "@/components/daily-view";
import TaskInputModal from "@/components/task-input-modal";
import AutoPlanModal from "@/components/auto-plan-modal";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

// Mock data for development
const mockTasks = [
  {
    id: "1",
    title: "Plan feature A for my app",
    description: "Define requirements and create wireframes",
    time: "09:00",
    isCompleted: false,
    isHighlight: true,
    priorityScore: 95,
    status: "todo" as const,
  },
  {
    id: "2",
    title: "Daily Standup",
    description: "Team sync meeting",
    time: "10:00",
    isCompleted: true,
    isHighlight: false,
    priorityScore: 60,
    status: "done" as const,
  },
  {
    id: "3",
    title: "Push to trm - Github Connection",
    description: "Deploy latest changes to production",
    time: "12:00",
    isCompleted: false,
    isHighlight: false,
    priorityScore: 80,
    status: "todo" as const,
  },
  {
    id: "4",
    title: "Plan a trip from PA to Rome",
    description: "Research flights and accommodations",
    time: "15:00",
    isCompleted: false,
    isHighlight: false,
    priorityScore: 40,
    status: "todo" as const,
  },
];

export default function Home() {
  // const { isSignedIn, user } = useUser();
  const isSignedIn = true; // Mock for development
  const user = { firstName: "Dev", lastName: "User" }; // Mock for development
  const [tasks, setTasks] = useState(mockTasks);
  const [showTaskInput, setShowTaskInput] = useState(false);
  const [showAutoPlan, setShowAutoPlan] = useState(false);
  const [selectedTaskForAutoPlan, setSelectedTaskForAutoPlan] = useState<string | null>(null);

  const handleTaskToggle = (taskId: string) => {
    setTasks(tasks.map(task => 
      task.id === taskId 
        ? { ...task, isCompleted: !task.isCompleted, status: task.isCompleted ? 'todo' : 'done' }
        : task
    ));
  };

  const handleTaskHighlight = (taskId: string) => {
    setTasks(tasks.map(task => 
      task.id === taskId 
        ? { ...task, isHighlight: !task.isHighlight }
        : { ...task, isHighlight: false } // Only one highlight allowed
    ));
  };

  const handleTaskDetails = (taskId: string) => {
    console.log('Opening task details for:', taskId);
    // TODO: Implement task details modal
  };

  const handleAutoLan = (taskId: string) => {
    setSelectedTaskForAutoPlan(taskId);
    setShowAutoPlan(true);
  };

  const handleTaskMove = (taskId: string, newTime: string) => {
    setTasks(tasks.map(task => 
      task.id === taskId 
        ? { ...task, time: newTime }
        : task
    ));
  };

  const handleTaskCreate = (parsedTasks: any[]) => {
    const newTasks = parsedTasks.map((task, index) => ({
      id: `generated-${Date.now()}-${index}`,
      title: task.title,
      description: task.description || '',
      time: task.dueDate ? new Date(task.dueDate).toTimeString().slice(0, 5) : '09:00',
      isCompleted: false,
      isHighlight: false,
      priorityScore: (task.priority || 3) * 20,
      status: 'todo' as const,
    }));
    
    setTasks([...tasks, ...newTasks]);
  };

  const handleSubtasksCreate = (subtasks: any[]) => {
    const newSubtasks = subtasks.map((subtask, index) => ({
      id: `subtask-${Date.now()}-${index}`,
      title: subtask.title,
      description: subtask.description || '',
      time: new Date(Date.now() + (index + 1) * 60 * 60 * 1000).toTimeString().slice(0, 5), // Space them out hourly
      isCompleted: false,
      isHighlight: false,
      priorityScore: subtask.priority * 20,
      status: 'todo' as const,
    }));
    
    setTasks([...tasks, ...newSubtasks]);
  };

  if (!isSignedIn) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Welcome to Orion</h1>
          <p className="text-muted-foreground">Please sign in to continue</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="relative h-full">
      <DailyView
        tasks={tasks}
        onTaskToggle={handleTaskToggle}
        onTaskHighlight={handleTaskHighlight}
        onTaskDetails={handleTaskDetails}
        onAutoLan={handleAutoLan}
        onTaskMove={handleTaskMove}
      />
      
      {/* Floating Action Button */}
      <Button
        onClick={() => setShowTaskInput(true)}
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow z-40"
        size="icon"
      >
        <Plus className="h-6 w-6" />
      </Button>

      {/* Task Input Modal */}
      <TaskInputModal
        isOpen={showTaskInput}
        onClose={() => setShowTaskInput(false)}
        onTaskCreate={handleTaskCreate}
      />

      {/* Auto Plan Modal */}
      {selectedTaskForAutoPlan && (
        <AutoPlanModal
          isOpen={showAutoPlan}
          onClose={() => {
            setShowAutoPlan(false);
            setSelectedTaskForAutoPlan(null);
          }}
          taskTitle={tasks.find(t => t.id === selectedTaskForAutoPlan)?.title || ''}
          taskDescription={tasks.find(t => t.id === selectedTaskForAutoPlan)?.description}
          onSubtasksCreate={handleSubtasksCreate}
        />
      )}
    </div>
  );
}
